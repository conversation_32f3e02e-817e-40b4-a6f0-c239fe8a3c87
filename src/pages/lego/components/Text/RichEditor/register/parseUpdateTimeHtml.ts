import { SlateElement } from '@wangeditor/editor';

/**
 * 解析 HTML 字符串，生成“附件”元素
 * @param domElem HTML 对应的 DOM Element
 * @param children 子节点
 * @param editor editor 实例
 * @returns “附件”元素，如上文的 myResume
 */
function parseUpdateTimeHtml(
  domElem: Element,
  // children: SlateDescendant[],
  // editor: IDomEditor
): SlateElement {
  // TS 语法
  // function parseAttachmentHtml(domElem, children, editor) {                                                     // JS 语法

  // 从 DOM element 中获取“附件”的信息
  // const link = domElem.getAttribute("data-link") || "";
  // const fileName = domElem.getAttribute("data-fileName") || "";
  const id = domElem.getAttribute('data-field-id') || '';

  // 生成“附件”元素（按照此前约定的数据结构）
  const myResume = {
    type: 'updateTime',
    // link,
    // fileName,
    id,
    children: [{ text: '' }], // void node 必须有 children ，其中有一个空字符串，重要！！！
  };

  return myResume;
}
const parseUpdateTimeHtmlConf = {
  selector: 'span[data-w-e-type="updateTime"]', // CSS 选择器，匹配特定的 HTML 标签
  parseElemHtml: parseUpdateTimeHtml,
};
export default parseUpdateTimeHtmlConf;
