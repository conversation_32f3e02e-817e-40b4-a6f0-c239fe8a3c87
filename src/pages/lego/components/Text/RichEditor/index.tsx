import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Editor, Toolbar } from '@wangeditor/editor-for-react';
import '@wangeditor/editor/dist/css/style.css'; // 引入 css
import {
  Boot,
  IDomEditor,
  IEditorConfig,
  IToolbarConfig,
  IButtonMenu,
  DomEditor,
} from '@wangeditor/editor';
import { useSize } from 'ahooks';
import useComponent from '@/pages/lego/hooks/useComponent';
import { setSelectComponentPropsDataById } from '@/pages/lego/utils';
import { checkTextSafe, queryDatasetUpdateTime } from '@/pages/lego/api';
import { register } from './register/updateTimeNode';
import './index.less';

class MyButtonMenu implements IButtonMenu {
  title: string;
  iconSvg: string;
  tag: string;
  constructor() {
    this.title = '更新时间'; // 自定义菜单标题
    // this.iconSvg = '<svg>...</svg>' // 可选
    this.iconSvg =
      '<svg class="icon" viewBox="0 0 1024 1024" ><path d="M512.141 962.492c-248.6 0-450.132-201.532-450.132-450.132S263.542 62.228 512.141 62.228 962.273 263.76 962.273 512.36 760.741 962.492 512.141 962.492z m0-818.422c-203.402 0-368.29 164.887-368.29 368.29s164.887 368.29 368.29 368.29 368.29-164.887 368.29-368.29-164.887-368.29-368.29-368.29z m163.685 409.211h-93.194c-14.175 24.36-40.275 40.921-70.491 40.921-45.201 0-81.842-36.641-81.842-81.842 0-30.216 16.561-56.316 40.921-70.491V266.833c0-22.601 18.32-40.921 40.921-40.921s40.921 18.32 40.921 40.921v175.036a82.974 82.974 0 0 1 29.741 29.57h93.022c22.601 0 40.921 18.32 40.921 40.921 0.001 22.601-18.32 40.921-40.92 40.921z" fill="#0A0A0A" p-id="2932"/></svg>';
    this.tag = 'button';
  }

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue(editor: IDomEditor): string | boolean {
    // TS 语法
    console.log(editor);
    return '';
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive(editor: IDomEditor): boolean {
    console.log(editor);
    return false;
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled(editor: IDomEditor): boolean {
    // TS 语法
    console.log(editor);
    return false;
  }
  update() {}

  // 点击菜单时触发的函数
  exec(editor: IDomEditor) {
    // TS 语法
    // exec(editor, value) {                              // JS 语法
    if (this.isDisabled(editor)) return;
    console.log(editor, 'exec!!!!!');
    const node = {
      type: 'updateTime',
      children: [{ text: '' }],
      id: '123456', // todo
    };
    // 插入 updateTime
    editor.insertNode(node);
  }
}
// 注册自定义节点
register();
// 自定义菜单
Boot.registerMenu({
  key: 'updateTime', // 菜单唯一key
  factory() {
    return new MyButtonMenu();
  },
});
const CONTAINER_HEIGHT = 300;
const MAX_LENGTH = 500;
const RichEditorReadOnly = (props: any) => {
  const [meta] = useComponent(props.componentId);
  const dataSourceId = props?.dataSetConfig?.dataSourceId;
  const textHtml = props.text || meta.text;
  const [updateTime, setUpdateTime] = useState('--');
  // 获取指标数据
  useEffect(() => {
    if (dataSourceId) {
      queryDatasetUpdateTime({ datasetId: dataSourceId })
        .then((res: any) => {
          setUpdateTime(res.data?.updateTime);
        })
        .catch(() => {
          setUpdateTime('--');
        });
    }
  }, [dataSourceId]);
  // 将带有[数据更新时间]替换为真正的返回值
  const textValue = useMemo(() => {
    const newHtml = textHtml?.replace(/\[数据更新时间\]/g, `${updateTime}`);
    return newHtml || '';
  }, [textHtml, updateTime]);

  return (
    <div
      className="lego-rich-edit-readonly"
      dangerouslySetInnerHTML={{ __html: textValue }}
    ></div>
  );
};

function RichEditor(props: any) {
  // props
  const [meta] = useComponent(props.componentId);
  const value = props.text || meta.text;
  const [tooltipTitle, setTooltipTitle] = useState('');
  // 编辑器实例
  const [editor, setEditor] = useState<IDomEditor | null>(null); // TS 语法
  // 工具栏配置
  const containerRef = useRef<HTMLDivElement>(null);
  const containerSize = useSize(containerRef);
  const [editorHeight, setEditorHeight] = useState(80);
  const toolbarConfig: Partial<IToolbarConfig> = {
    excludeKeys: [
      'group-image',
      'todo',
      'blockquote',
      'emotion',
      'group-image',
      'group-video',
      'group-more-style',
      'group-indent',
      'insertTable',
      'codeBlock',
      'fullScreen',
      'undo',
      'redo',
      'lineHeight',
      'fontFamily',
      'divider',
      'group-justify',
    ],
  }; // TS 语法
  toolbarConfig.insertKeys = {
    index: 31, // 插入的位置，基于当前的 toolbarKeys
    keys: ['updateTime'],
  };
  // 敏感字段展示（红色）
  const formatContentHtml = (result: any = {}) => {
    const { suggestion } = result;
    // 插入链接的字符转成#[]
    if (suggestion === 'block') {
      const txt = `文本内容涉及敏感，请修改或者删除`;
      setTooltipTitle(txt);
      // message.error(txt)
    }
  };
  // 失去焦点校验敏感词汇
  const onBlur = async (val: string) => {
    (window.top.proxy || window.top).__lego_checkTextSafe = true;

    let checkRes: any = {};
    if (val !== '') {
      checkRes = await checkTextSafe({
        contents: [val],
      });
    }

    if (val !== '' && checkRes.data?.results[0]?.suggestion === 'block') {
      formatContentHtml(checkRes.data.results[0]);
    } else {
      setTooltipTitle('');
      setSelectComponentPropsDataById(props.componentId, 'text', val);
    }
    (window.top.proxy || window.top).__lego_checkTextSafe = false;
  };
  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {
    placeholder: '请输入内容...',
    onBlur: (editor: IDomEditor) => {
      const val = editor.getHtml();
      onBlur(val);
    },
    maxLength: MAX_LENGTH,
    MENU_CONF: {
      fontSize: {
        fontSizeList: ['14px', '16px', '18px', '20px', '22px'],
      },
    },
  };

  // 及时销毁 editor ，重要！
  useEffect(() => {
    if (editor) {
      console.log(editor, 'editoreditor');
      editor.children.forEach((item: any) => {
        item.children?.forEach((element: any) => {
          element.fontSize = '14px';
        });
      });
    }
    return () => {
      if (editor === null) return;
      editor.destroy();
      setEditor(null);
    };
  }, [editor]);
  // 动态计算高度。因为编辑器的工具栏折行不固定
  useEffect(() => {
    if (containerRef?.current && containerSize) {
      const targetNode: any =
        containerRef.current.querySelector('.w-e-toolbar');
      if (targetNode) {
        const height = targetNode.offsetHeight;
        const edHeight = containerSize?.height - height - 20;
        setEditorHeight(edHeight);
        // const toolBarSelectList: any =
        //   containerRef.current.querySelectorAll('.w-e-select-list');
        // for (let index = 0; index < toolBarSelectList.length; index++) {
        //   const element = toolBarSelectList[index];
        //   element.style.height = `${edHeight - 20}px !important`;
        //   console.log(element.style.height, 'element.style.height');
        // }
      }
    }
  }, [containerSize?.width, containerSize?.height]);
  const getContent = () => {
    const getHtmlStr = editor?.getHtml();
    if (editor) {
      const toolbar = DomEditor.getToolbar(editor);
      const curToolbarConfig = toolbar?.getConfig();
      console.log(curToolbarConfig, 'curToolbarConfig');
    }
    console.log(getHtmlStr, 'getHtmlStr');
  };
  const setContent = (html: string) => {
    if (editor) {
      editor?.setHtml(html);
      setTimeout(() => {
        editor?.focus(true);
      }, 100);
    }
  };

  const onChange = (editor: IDomEditor) => {
    // console.log(editor.selection, 'kkkkk');
  };

  useEffect(() => {
    if (editor && value) {
      setContent(value);
    }
  }, [value, editor]);

  return (
    <div className="lego-rich-edit-wrap">
      {props.__designMode === 'design' ? (
        <>
          <div
            data-edit
            style={{ height: `100%`, width: '100%' }}
            ref={containerRef}
          >
            <Toolbar
              editor={editor}
              defaultConfig={toolbarConfig}
              mode="default"
              style={{
                borderBottom: '1px solid rgba(37, 52, 79, 0.0784)',
                position: 'relative',
              }}
            />
            <Editor
              data-edit
              defaultConfig={editorConfig}
              // value={value}
              onCreated={setEditor}
              onChange={(editor: IDomEditor) => onChange(editor)}
              mode="default"
              style={{ height: editorHeight, overflowY: 'hidden' }}
            />
          </div>
          {props.__designMode === 'design' && tooltipTitle && (
            <div className="errorInfo">文本涉及敏感字段</div>
          )}
        </>
      ) : (
        <RichEditorReadOnly {...props} />
      )}
    </div>
  );
}

export default RichEditor;
