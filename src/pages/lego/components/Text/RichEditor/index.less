.lego-rich-edit-wrap {
  border: 1px solid rgba(37, 52, 79, 0.0784);
  width: 100%;
  height: calc(100% - 10px);
  position: relative;
  .data-w-e-toolbar {
    border-bottom: 1px solid rgba(37, 52, 79, 0.0784) !important;
  }
  .errorInfo {
    position: absolute;
    left: 0px;
    bottom: -10px;
    display: flex;
    align-items: center;
    color: red;

    svg {
      color: red;
      margin-left: 2px;
    }
  }
}
.lego-rich-edit-readonly {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.9);
  span[data-w-e-type='updateTime'] {
    color: #366cfe;
  }
}
