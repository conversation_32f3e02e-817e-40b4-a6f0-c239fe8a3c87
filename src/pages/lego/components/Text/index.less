.lego-text-wrap {
  padding: 10px 0 0;
  height: 100%;
  &.mobile {
    padding: 0;
    .blm-ellipsis {
      font-size: 14px;
    }
  }
  .ant-input-textarea-affix-wrapper {
    height: calc(100% - 20px) !important;
  }

  textarea {
    height: 100% !important;
    border: 1px solid transparent !important;
    background: rgba(37, 52, 79, 0.03) !important;
    &:focus {
      background-color: transparent !important;
    }
  }

  textarea.ant-input-disabled {
    color: #303133;
    background: transparent !important;

    &:hover {
      cursor: auto;
    }
  }

  .ant-input-affix-wrapper {
    border-color: #fff;

    &:focus-within {
      border-color: #1677ff;

      .ant-input {
        background: #fff;
      }
    }

    .ant-input {
      background: rgba(37, 52, 79, 0.03);
    }
  }

  .errorInfo {
    position: absolute;
    left: 14px;
    bottom: 14px;
    display: flex;
    align-items: center;
    color: red;

    svg {
      color: red;
      margin-left: 2px;
    }
  }
}

// 滚动条默认隐藏，滚动显示
.lego-bi-scroll-hide textarea::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.lego-bi-scroll-hide textarea::-webkit-scrollbar-thumb {
  background-color: transparent;
}

.lego-bi-scroll-hide textarea:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
}
.lego-text-popup-wrapper {
  .blm-popup-body {
    padding: 12px;
    .lego-text-popup-header {
      line-height: 22px;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    .lego-text-popup-content {
      font-size: 14px;
      line-height: 22px;
      max-height: 70vh;
      overflow-y: auto;
      color: rgba(0, 0, 0, 0.9);
    }
  }
}

.blm-popup-body {
  .lego-text-popup-content {
    white-space: pre-wrap;
  }
}
