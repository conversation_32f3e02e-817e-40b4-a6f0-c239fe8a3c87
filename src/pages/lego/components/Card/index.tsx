import React, { useRef, useEffect, useState } from 'react';
import { Card as UICard, Tooltip, Popover } from '@blmcp/ui';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
// import { ExclamationCircleOutlined } from '@ant-design/icons';
import { formatValue } from '@/pages/lego/libraryMaterials/module/utils';
import echarts from '../echarts';
import { getTextyWidthAndHeight } from '../../utils/css';
import { Colors } from '../constant';
import { isValueValid } from '../../libraryMaterials/module/utils';
import isMobile from '../../utils/isMobile';
import TooltipIcon from './image/tooltip.svg';
import DrageIcon from './image/drag.svg';
import './index.less';
import { isSetHeight } from './tools';

export const Card = ({
  useResize,
  dataSource,
  height,
  width,
  __designMode,
}: any) => {
  const Height = height + 60;
  const wrapper = useRef(null);
  const [explainTextHeight, setExplainTextHeight] = useState<number | null>(
    Height ? Height - 146 : 36,
  );
  const ref = useRef(null);
  const myChart = useRef<any>(null);
  const formateValue = (value: any) => {
    if (isValueValid(value)) {
      return (
        (
          Math.round(Number(String(value).replace('+', '')) * 10000) / 100
        ).toFixed(2) + '%'
      );
    }
    return '-';
  };
  useEffect(() => {
    return useResize(function () {
      myChart?.current?.resize?.();
    });
  }, []);
  useEffect(() => {
    const dom = ref.current;
    myChart.current = echarts().init(dom);
    const value = dataSource?.values || [];
    // mom:环比 yoy:同比
    let series: any = [];
    if (value?.[0]) {
      series = Object.keys(value[0])
        .filter((key: string) => key !== 'FTE')
        .map((key: any) => {
          const map: any = {
            mom: '环比',
            yoy: '同比',
          };
          const name = map[key];
          return {
            name,
            type: 'line',
            // 如果只有一个点需要显示showSymbol，不然没有任何内容
            showSymbol: dataSource?.values?.length === 1,
            data: value.map((item: any) => item[key]),
            smooth: true,
          };
        });
    }

    myChart.current.setOption({
      color: Colors,
      grid: {
        top: 4,
        bottom: 12,
        left: 0,
        right: 0,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        // confine: true,
        formatter: function (item: any) {
          const title = item[0].name;
          let html: string[] = [];
          item.forEach((axis: any, index: number) => {
            const matchColor = axis.marker?.match(/background-color:([^]*?);/);
            const color =
              (matchColor && matchColor[1]) || Colors[index % Colors.length];
            html.push(
              `<tr><td><span style="display:inline-block;width: 8px;height: 2px;vertical-align: middle;background-color: ${color}"></span> ${
                axis.seriesName
              }</td><td style="text-align: right;font-weight: 500;">
              ${(Math.round(axis.value * 10000) / 100).toFixed(2) + '%'}
              </td></tr>`,
            );
          });
          const table = `<table>
          <tr colspan="2"><td style="font-weight: 500;">${title}</td></tr>
          ${html.join('')}
        </table>`;
          return table;
        },
        position: function (point: Array) {
          return [point[0] - (isMobile() ? 180 : 100), point[1] + 10];
        },
        // echarts 升级5.5 需要使用 appendTo
        appendToBody: !isMobile(),
        // valueFormatter: (value: any) => (Math.round(value * 10000) / 100).toFixed(2) + '%'
      },
      xAxis: {
        show: false,
        type: 'category',
        data: value.map((item: any) => item?.FTE),
      },
      yAxis: {
        show: false,
        type: 'value',
      },
      series,
    });
    myChart.current.resize();
    const touchstart = (e: any) => {
      let { target } = e;
      let isInner = false;
      while (target) {
        if (target === dom) {
          isInner = true;
          break;
        }
        target = target.parentNode;
      }
      if (!isInner) {
        myChart.current.dispatchAction({
          type: 'hideTip',
        });
      }
    };
    document.addEventListener('touchstart', touchstart);
    return () => {
      document.removeEventListener('touchstart', touchstart);
      if (dom) {
        echarts().dispose(dom);
      }
    };
  }, [dataSource]);
  // 上升
  const uptri = (
    <CaretUpOutlined style={{ padding: '0 3px', color: '#DB3241' }} />
  );
  // 下降
  const downtri = (
    <CaretDownOutlined style={{ padding: '0 3px', color: '#22981C' }} />
  );
  const isUp = (value: number | string) => String(value).indexOf('-') === -1;
  useEffect(() => {
    if (isSetHeight(wrapper?.current)) {
      // 如果设置了高度，则设置explainTextHeight，如果没设置，则是自适应高度
      setExplainTextHeight(height + 60 - 146);
    } else {
      setExplainTextHeight(null);
    }
  }, [height]);
  const explainTextStyle: any = {
    height: explainTextHeight ? explainTextHeight + 'px' : undefined,
    WebkitLineClamp: Height ? Math.ceil((Height - 146) / 18) : 2, // 这里的公式是试验出来的
  };

  if (Height && (Height - 146) % 18 < 4 && (Height - 146) % 18 > 0) {
    // 这里的公式是试验出来的
    explainTextStyle.WebkitLineClamp--;
  }
  if (!explainTextHeight) {
    explainTextStyle.WebkitLineClamp = undefined;
  }
  // 计算value的宽度
  // 如果没有出现省略号，则不显示tooltip
  const formatValueStr: any = isValueValid(dataSource?.value)
    ? formatValue(dataSource?.value, dataSource?.measureInfo?.[0])
    : '-';
  const showValueToolTip =
    getTextyWidthAndHeight(
      formatValueStr,
      {
        float: 'left',
        'font-size': '28px',
        'font-weight': '700',
        'white-space': 'nowrap',
        'font-family': 'PingFang SC',
      },
      'div',
    ).width >
    (width - 6) * 0.55;
  const showExplainTextToolTip =
    !!dataSource?.values?.length && !!dataSource?.explain;
  return (
    <div
      className="lego-card-com-wrap"
      ref={wrapper}
      style={{ height: '100%' }}
    >
      <UICard style={{ width: '100%', height: '100%' }}>
        <div className="lego-card-title">
          {__designMode === 'design' && (
            <DrageIcon style={{ marginRight: '4px', verticalAlign: 'sub' }} />
          )}
          {dataSource?.measureInfo?.[0]?.alias ||
            dataSource?.measureInfo?.[0]?.title ||
            '-'}
          {
            // 按产品要求，没有toolTip的时候不显示
            dataSource?.measureInfo?.[0]?.tips ? (
              <Tooltip
                title={dataSource?.measureInfo?.[0]?.tips}
                placement={isMobile() ? 'right' : 'top'}
              >
                <span
                  style={{
                    marginLeft: '3px',
                    position: 'relative',
                    top: '2px',
                  }}
                >
                  <TooltipIcon></TooltipIcon>
                </span>
              </Tooltip>
            ) : undefined
          }
          {/* {dataSource?.showFTP && (
            <Tooltip
              arrow={{ pointAtCenter: true }}
              title="部分图表的数据集中缺少字段，筛选条件未生效"
              placement="topRight"
            >
              <ExclamationCircleOutlined className="lego-card-com-right-info" />
            </Tooltip>
          )} */}
        </div>
        <div style={{ paddingLeft: '6px' }}>
          <Tooltip title={showValueToolTip ? formatValueStr : ''}>
            <div className="lego-card-measure-value">{formatValueStr}</div>
          </Tooltip>
          <div className="lego-card-measure-trend" ref={ref} />
          <div style={{ clear: 'both' }} />
        </div>
        <div className="lego-card-measure-value-compare">
          {isValueValid(dataSource?.mom) && (
            <>
              环比
              {dataSource?.mom !== 0 ? (
                isUp(dataSource?.mom) ? (
                  uptri
                ) : (
                  downtri
                )
              ) : (
                <span style={{ padding: '0 3px 0 0' }}></span>
              )}
              <span
                style={{
                  padding: '0 8px 0 0',
                  color:
                    dataSource?.mom !== 0
                      ? isUp(dataSource?.mom)
                        ? '#DB3241'
                        : '#22981C'
                      : 'rgba(0, 0, 0, 0.4)',
                }}
              >
                {formateValue(dataSource?.mom)}
              </span>
            </>
          )}
          {isValueValid(dataSource?.yoy) && (
            <>
              同比
              {dataSource?.yoy !== 0 ? (
                isUp(dataSource?.yoy) ? (
                  uptri
                ) : (
                  downtri
                )
              ) : (
                <span style={{ padding: '0 3px 0 0' }}></span>
              )}
              <span
                style={{
                  paddingLeft: '0',
                  color:
                    dataSource?.yoy !== 0
                      ? isUp(dataSource?.yoy)
                        ? '#DB3241'
                        : '#22981C'
                      : 'rgba(0, 0, 0, 0.6)',
                }}
              >
                {formateValue(dataSource?.yoy)}
              </span>
            </>
          )}
        </div>
        <Popover
          content={
            showExplainTextToolTip ? (
              <div style={{ maxWidth: '200px', lineBreak: 'anywhere' }}>
                {dataSource?.explain}
              </div>
            ) : undefined
          }
          placement="bottom"
          title={
            showExplainTextToolTip ? (
              <div style={{ color: '#ED7B2F' }}>数据解读</div>
            ) : undefined
          }
        >
          <div
            className={`lego-card-measure-value-explain ${
              showExplainTextToolTip ? 'tooltip' : ''
            }`}
          >
            <div
              className="lego-card-measure-value-explain-text"
              style={explainTextStyle}
            >
              <span>{dataSource?.overView || '-'}</span>
            </div>
          </div>
        </Popover>
      </UICard>
    </div>
  );
};
