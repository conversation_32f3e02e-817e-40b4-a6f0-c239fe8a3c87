import React from 'react';
import { Card as UICard, Tooltip } from '@blmcp/ui';
import {
  formatValue,
  isValueValid,
} from '@/pages/lego/libraryMaterials/module/utils';
import { getTextyWidthAndHeight } from '../../utils/css';
import isMobile from '../../utils/isMobile';
import TooltipIcon from './image/tooltip.svg';
import DrageIcon from './image/drag.svg';
import './index.less';
// import { ExclamationCircleOutlined } from '@ant-design/icons';

export const IndexCard = ({ dataSource, width, height, __designMode }: any) => {
  // 计算value的宽度
  // 如果没有出现省略号，则不显示tooltip
  const formatValueStr: any = isValueValid(dataSource?.value)
    ? formatValue(dataSource?.value, dataSource?.measureInfo?.[0])
    : '-';
  const showValueToolTip =
    getTextyWidthAndHeight(
      formatValueStr,
      {
        float: 'left',
        'font-size': '28px',
        'font-weight': '700',
        'white-space': 'nowrap',
        'font-family': 'PingFang SC',
      },
      'div',
    ).width >
    width - 33; // 33为盒子的左右padding

  const valueHeight = height + 60 - 12 * 2 - 20 + 'px';
  return (
    <div
      className={`lego-index-card-com-wrap ${isMobile() ? 'mobile' : ''}`}
      style={{ height: '100%' }}
    >
      <UICard style={{ width: '100%', height: '100%' }}>
        <div className="lego-index-card-title">
          {__designMode === 'design' && (
            <DrageIcon style={{ marginRight: '4px', verticalAlign: 'sub' }} />
          )}
          {/* 有别名展示别名，否则展示原字段名 */}
          {dataSource?.measureInfo?.[0]?.alias ||
            dataSource?.measureInfo?.[0]?.title ||
            '-'}
          {
            // 按产品要求，没有toolTip的时候不显示
            dataSource?.measureInfo?.[0]?.tips ? (
              <Tooltip
                title={dataSource?.measureInfo?.[0]?.tips}
                placement={isMobile() ? 'right' : 'top'}
              >
                <span
                  style={{
                    marginLeft: '3px',
                    position: 'relative',
                    top: '2px',
                  }}
                >
                  <TooltipIcon></TooltipIcon>
                </span>
              </Tooltip>
            ) : undefined
          }
        </div>
        <div style={{ paddingLeft: '6px' }}>
          <Tooltip title={showValueToolTip ? formatValueStr : ''}>
            <div
              className="lego-index-card-measure-value"
              style={{ height: valueHeight, lineHeight: valueHeight }}
            >
              {formatValueStr}
            </div>
          </Tooltip>
        </div>
      </UICard>
    </div>
  );
};
