import { defineConfig } from '@umijs/max';
import { Token } from '@blmcp/ui';
import routes from '../routes';
import { externals, proxy } from './project.config';

const baseUrl = `https://webstatic.${
  process.env.NODE_ENV === 'development'
    ? 'yueyuechuxing.cn'
    : '<%= mainDomain %>'
}/yueyue/admin`;

const devStyle: any = [
  {
    rel: 'stylesheet',
    href: 'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/css/blmcpUi-umdV1.1.20-1a10201923.css',
  },
  {
    rel: 'stylesheet',
    href: 'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/css/blmBusinessComponents-umdV0.0.66-64b8110642.css',
  },
];
const devScripts = [
  'window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__="http://localhost:8000/"',
  'https://webstatic.yueyuechuxing.cn/yueyue/admin/lego-base/v1.0.0/react.development.js',
  'https://webstatic.yueyuechuxing.cn/yueyue/admin/lego-base/v1.0.0/react-dom.development.js',
  'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/antd-umdV1.11.10-2bf27bc00d.js',
  // 'https://cdntest.yueyuechuxing.cn/yueyue/admin/static_umd/daily/cp/js/antdIcons-umdV5.5.1-05b7122c56.js',
  // 'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/blmMobileLib-umdV1.1.0-38ef4ddafa.js',
  // 'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/blmcpUi-umdV1.1.20-1b21c26a29.js',
  // 'https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/blmBusinessComponents-umdV0.0.66-5f3419699b.js',
];

// aliLowCodeEngine文件加载处理
const aliLowCodeEngineEnv = 'cdntest';
// const aliLowCodeEngineEnv = 'webstatic';
const aliLowCodeEngineVersion = '1.0.1-beta.110';
// const AliLowCodeEngineURl = 'http://localhost:5555/js/AliLowCodeEngine.js';
const AliLowCodeEngineURl = `https://${aliLowCodeEngineEnv}.yueyuechuxing.cn/yueyue/admin/blmlclowcode-ignitor/${aliLowCodeEngineVersion}/js/AliLowCodeEngine.js`;
export default defineConfig({
  esbuildMinifyIIFE: true,
  // 指定npm客户端
  npmClient: 'pnpm',
  tailwindcss: {},
  icons: {},
  mock: {},
  qiankun: {
    slave: {
      enable: true,
      // https://github.com/umijs/umi/blob/master/packages/plugins/src/qiankun/slave.ts#L189
      shouldNotAddLibraryChunkName: true,
    },
  },
  plugins: ['@blmcp/peento-umi-extension'],
  devtool: 'source-map',
  externals,
  proxy,
  routes,
  antd: {
    theme: {
      token: Token.antdToken,
    },
  },
  define: {
    'process.env.buildDate': Date.now(),
    'process.env.BUILD_ENV': process.env.BUILD_ENV,
  },
  links: process.env.NODE_ENV === 'development' ? devStyle : [],
  headScripts: (process.env.NODE_ENV === 'development'
    ? devScripts
    : []
  ).concat([
    `${baseUrl}/lego-base/v1.0.0/prop-types.js`,
    // `${baseUrl}/lego-base/v1.0.0/react15-polyfill.js`,
    {
      src: `${baseUrl}/lego-base/v1.0.0/lodash.min.js`,
      ignore: 'true',
    },
    {
      src: `${baseUrl}/lego-base/v1.0.0/moment.min.js`,
      ignore: 'true',
    },
    `window.currentScript = "${AliLowCodeEngineURl}";`,
  ] as any),
});
