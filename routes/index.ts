export default [
  {
    path: '/welcome',
    component: '@/pages/welcome',
    name: 'welcome',
  },
  {
    path: '/403',
    component: '@/pages/403',
    name: '403',
  },
  // {
  //   path: '/404',
  //   component: '@/pages/404',
  //   name: '404',
  // },
  {
    path: '/404',
    component: '@/pages/sub',
    name: '404',
  },
  {
    name: '数据乐高-新建编辑',
    path: '/legoBI/edit',
    pageKey: 'legoReportEditBI',
    component: './newLego/editPage',
  },
  {
    name: '数据乐高-查看',
    path: '/legoBI/view',
    pageKey: 'legoReportViewBI',
    component: './newLego/viewPage',
  },
  {
    name: '数据乐高-报表列表',
    path: '/legoBI/list',
    pageKey: 'legoReportListBI',
    component: './newLego/pageList',
  },
  {
    name: '数据乐高-权限设置页',
    path: '/legoBI/permission',
    pageKey: 'legoPermissionBI',
    component: './newLego/permission',
  },
  {
    name: '人工标签-圈选',
    path: '/tagCircleSelection',
    pageKey: 'tagCircleSelectionBI',
    component: './userPortrait/index',
  },
  {
    name: '租户组件demo',
    path: '/tenantListDemo',
    pageKey: 'tenantListDemoBI',
    component: './tenantListDemo/index',
  },
  {
    name: '数据乐高-菜单报告',
    path: '/legoBI/report/:pageName',
    pageKey: 'legoReportPage',
    component: './newLego/reportPage',
  },
  {
    name: '人群包4.1demo',
    path: '/crowdComponentsDemo',
    pageKey: 'crowdComponentsDemoReact',
    component: './test/index',
  },
  {
    name: '数据乐高-菜单报告',
    path: '/report/:pageName',
    pageKey: 'newLegoReportPage',
    component: './newLego/reportPage',
  },
];
